import React from 'react';
import {
    CheckSquare,
    ChevronDown,
    ExternalLink,
    Edit2,
    X,
    Check,
    MessageSquare,
    Sparkles
} from 'lucide-react';

type EnhancedFinalValue = {
    Attribute?: string;
    finalValue: string;
    source: string;
    walmartValidation?: string;
    llmValidation?: string;
    walmartComment?: string;
    llmComment?: string;
    finalValidation?: string;
    finalComment?: string;
    finalVerdict?: string;
    rawData?: any;
};

interface Props {
    tableRows: Record<string, string>[];
    selectedCells: Set<string>;
    handleCellToggle: (rowIdx: number, col: string) => void;
    handleRowToggle: (rowIdx: number, row: Record<string, string>) => void;
    editableWalmartValues: Record<number, string>;
    originalWalmartValues: Record<number, string>;
    editingCell: number | null;
    setEditingCell: (n: number | null) => void;
    saveEdit: (rowIdx: number) => void;
    cancelEdit: () => void;
    llmValues: Record<number, string>;
    rawJson: string;
    competitorUrls: Record<string, string>;
    brandUrl: string;
    llmSourceUrl: string;
    walmartValidation: Record<number, string>;
    llmValidation: Record<number, string>;
    setWalmartValidation: (fn: any) => void;
    setLlmValidation: (fn: any) => void;
    walmartComments: Record<number, string>;
    llmComments: Record<number, string>;
    setWalmartComments: (fn: any) => void;
    setLlmComments: (fn: any) => void;
    walmartLatestValidation: Record<number, string>;
    walmartLatestComments: Record<number, string>;
    setWalmartLatestValidation: (fn: any) => void;
    setWalmartLatestComments: (fn: any) => void;
    setEditableWalmartValues: (fn: any) => void;
    finalValues: EnhancedFinalValue[];
    loading: boolean;
    attrMap: Record<string, string>;
    exportToWideFormatCSV?: () => void;
    generateInsights?: () => void;
    insightsLoading?: boolean;
    finalVerdictsState: Record<number, string>;
    setFinalVerdictsState: (fn: any) => void;
    editingFinalVerdict: number | null;
    setEditingFinalVerdict: (n: number | null) => void;
    generateAutoFinalVerdict: (rowIdx: number) => string;
}

const ResponseTable: React.FC<Props> = (props) => {
    const {
        tableRows,
        selectedCells,
        handleCellToggle,
        handleRowToggle,
        editableWalmartValues,
        originalWalmartValues,
        editingCell,
        setEditingCell,
        saveEdit,
        cancelEdit,
        llmValues,
        rawJson,
        competitorUrls,
        walmartValidation,
        llmValidation,
        setWalmartValidation,
        setLlmValidation,
        walmartComments,
        llmComments,
        setWalmartComments,
        setLlmComments,
        walmartLatestValidation,
        walmartLatestComments,
        setWalmartLatestValidation,
        setWalmartLatestComments,
        setEditableWalmartValues,
        finalValues,
        loading,
        attrMap,
        exportToWideFormatCSV,
        generateInsights,
        insightsLoading,
        finalVerdictsState,
        setFinalVerdictsState,
        editingFinalVerdict,
        setEditingFinalVerdict,
        generateAutoFinalVerdict
    } = props;

    // Helper function to get header colors
    const getHeaderColor = (col: string): string => {
        // Walmart family - Blue theme
        if (col === 'Walmart' || col === 'Validate Walmart' || col === 'Prefilled Value Comments') {
            return 'bg-blue-600 dark:bg-blue-700';
        }
        // Walmart Latest family - Teal theme  
        if (col === 'Walmart_Latest' || col === 'Validate Walmart Latest' || col === 'Walmart Latest Comments') {
            return 'bg-blue-600 dark:bg-blue-700';
        }
        // LLM family - Purple theme
        if (col === 'LLM' || col === 'Validate LLM' || col === 'LLM Comment') {
            return 'bg-blue-600 dark:bg-blue-700';
        }
    
        // Rest columns - Gray theme
        return 'bg-blue-600 dark:bg-blue-700';
    };

    // Helper function to get cell background colors
    const getCellBackgroundColor = (col: string, hasData: boolean): string => {
        if (!hasData) {
            return 'bg-gray-50 dark:bg-gray-900'; // Default gray for empty data
        }

        // Walmart family - Light blue
        if (col === 'Walmart' || col === 'Validate Walmart' || col === 'Prefilled Value Comments') {
            return 'bg-blue-50 dark:bg-blue-950/30';
        }
        // Walmart Latest family - Light teal
        if (col === 'Walmart_Latest' || col === 'Validate Walmart Latest' || col === 'Walmart Latest Comments') {
            return 'bg-teal-50 dark:bg-teal-950/30';
        }
        // LLM family - Light purple
        if (col === 'LLM' || col === 'Validate LLM' || col === 'LLM Comment') {
            return 'bg-purple-50 dark:bg-purple-950/30';
        }
        
        // Rest columns - Light gray
        return 'bg-gray-50 ';
    };

    const getOrderedTableRows = (rows: Record<string, string>[]) => {
        if (rows.length === 0) return rows;

        const filteredRows = rows.filter(row => {
            const attr = row.Attribute || '';
            return !attr.includes('Product Type') &&
                !attr.toLowerCase().includes('product type');
        });

        const priorityRows: Record<string, string>[] = [];
        const otherRows: Record<string, string>[] = [];

        filteredRows.forEach(row => {
            const attr = row.Attribute || '';
            let isPriority = false;

            // Check for Short Description variations
            if (attr.includes('Short Description') ||
                attr.toLowerCase().includes('short description')) {
                isPriority = true;
            }
            // Check for Product Long Description variations  
            else if (attr.includes('Product Long Description') ||
                attr.toLowerCase().includes('product long description')) {
                isPriority = true;
            }
            // Check for Primary Image / Main image variations
            else if (attr.includes('Primary Image') ||
                attr.includes('Main image') ||
                attr.toLowerCase().includes('primary image') ||
                attr.toLowerCase().includes('main image')) {
                isPriority = true;
            }
            // Check for Secondary Image variations
            else if (attr.includes('Secondary Image') ||
                attr.includes('Product Secondary Image URL') ||
                attr.toLowerCase().includes('secondary image') ||
                attr.toLowerCase().includes('product secondary image')) {
                isPriority = true;
            }

            if (isPriority) {
                priorityRows.push(row);
            } else {
                otherRows.push(row);
            }
        });

        const sortedPriorityRows: Record<string, string>[] = [];

        // 1. Short Description first
        const shortDesc = priorityRows.find(row => {
            const attr = row.Attribute || '';
            return attr.includes('Short Description') ||
                attr.toLowerCase().includes('short description');
        });
        if (shortDesc) sortedPriorityRows.push(shortDesc);

        // 2. Product Long Description second
        const longDesc = priorityRows.find(row => {
            const attr = row.Attribute || '';
            return attr.includes('Product Long Description') ||
                attr.toLowerCase().includes('product long description');
        });
        if (longDesc) sortedPriorityRows.push(longDesc);

        // 3. Primary/Main Image third
        const primaryImg = priorityRows.find(row => {
            const attr = row.Attribute || '';
            return attr.includes('Primary Image') ||
                attr.includes('Main image') ||
                attr.toLowerCase().includes('primary image') ||
                attr.toLowerCase().includes('main image');
        });
        if (primaryImg) sortedPriorityRows.push(primaryImg);

        // 4. Secondary Image fourth
        const secondaryImg = priorityRows.find(row => {
            const attr = row.Attribute || '';
            return attr.includes('Secondary Image') ||
                attr.includes('Product Secondary Image URL') ||
                attr.toLowerCase().includes('secondary image') ||
                attr.toLowerCase().includes('product secondary image');
        });
        if (secondaryImg) sortedPriorityRows.push(secondaryImg);

        // Add any remaining priority rows that weren't categorized above
        priorityRows.forEach(row => {
            if (!sortedPriorityRows.includes(row)) {
                sortedPriorityRows.push(row);
            }
        });

        return [...sortedPriorityRows, ...otherRows];
    };

    const getDataRowIndex = (visualRowIdx: number) => {
        const orderedRows = getOrderedTableRows(tableRows);
        const visualRow = orderedRows[visualRowIdx];
        return tableRows.findIndex(row => row.Attribute === visualRow.Attribute);
    };

    const handleCellToggleOrdered = (visualRowIdx: number, col: string) => {
        const dataRowIdx = getDataRowIndex(visualRowIdx);
        handleCellToggle(dataRowIdx, col);
    };

    const handleRowToggleOrdered = (visualRowIdx: number, row: Record<string, string>) => {
        const dataRowIdx = getDataRowIndex(visualRowIdx);
        handleRowToggle(dataRowIdx, row);
    };

    const cleanNaNValue = (value: any): string => {
        if (value === null || value === undefined || value === '' ||
            String(value).toLowerCase() === 'nan' ||
            String(value).toLowerCase() === 'null') {
            return '-';
        }
        return String(value);
    };

    const isURL = (text: string): boolean => {
        try {
            new URL(text);
            return true;
        } catch {
            return false;
        }
    };

    const isWalmartCellSelected = (visualRowIdx: number): boolean => {
        const dataRowIdx = getDataRowIndex(visualRowIdx);
        return selectedCells.has(`${dataRowIdx}-Walmart`);
    };

    const isWalmartLatestCellSelected = (visualRowIdx: number): boolean => {
        const dataRowIdx = getDataRowIndex(visualRowIdx);
        return selectedCells.has(`${dataRowIdx}-Walmart_Latest`);
    };

    const isLlmCellSelected = (visualRowIdx: number): boolean => {
        const dataRowIdx = getDataRowIndex(visualRowIdx);
        return selectedCells.has(`${dataRowIdx}-LLM`);
    };

    const getValidationConfig = (status: string) => {
        switch (status) {
            case 'Yes':
                return { icon: <Check className="w-4 h-4" />, color: 'text-green-600', bgColor: 'bg-green-100', label: 'Yes' };
            case 'No':
                return { icon: <X className="w-4 h-4" />, color: 'text-red-600', bgColor: 'bg-red-100', label: 'No' };
            default:
                return { icon: null, color: 'text-gray-400', bgColor: 'bg-gray-50', label: 'Select Status' };
        }
    };

    const getTableHeaders = () => {
        if (tableRows.length === 0) return [];

        const baseHeaders = Object.keys(tableRows[0]);
        const orderedHeaders: string[] = [];

        orderedHeaders.push('Attribute');
        if (baseHeaders.includes('Walmart')) {
            orderedHeaders.push('Walmart', 'Validate Walmart', 'Prefilled Value Comments');
        }
        if (baseHeaders.includes('Walmart_Latest')) {
            orderedHeaders.push('Walmart_Latest', 'Validate Walmart Latest', 'Walmart Latest Comments');
        }
        orderedHeaders.push('LLM', 'Validate LLM', 'LLM Comment');
        baseHeaders.forEach(header => {
            if (!['Attribute', 'Walmart', 'Walmart_Latest', 'LLM'].includes(header) &&
                !header.includes('Validate') &&
                !header.includes('Comment') &&
                !header.includes('Final')) {
                orderedHeaders.push(header);
            }
        });
        orderedHeaders.push('Final Validation', 'Final Comment', 'Final Value', 'Source', 'Final Verdict');

        return orderedHeaders;
    };

    const renderCompetitorHeader = (col: string) => {
        const url = competitorUrls[col];
        if (url) {
            return (
                <button
                    onClick={() => window.open(url, '_blank', 'noopener,noreferrer')}
                    className="flex items-center justify-center gap-2 text-sm font-bold text-white hover:text-gray-300 hover:underline transition-colors cursor-pointer group w-full"
                    title={`Visit ${col} - ${url}`}
                >
                    <span>{col}</span>
                    <ExternalLink className="w-3 h-3 opacity-60 group-hover:opacity-100" />
                </button>
            );
        }
        return <span className="text-sm font-bold break-words" title={col}>{col}</span>;
    };

    const getColumnWidth = (): string => {
        return "w-64 min-w-64 max-w-64";
    };

    const renderTableCell = (col: string, visualRowIdx: number, row: Record<string, string>) => {
        const dataRowIdx = getDataRowIndex(visualRowIdx);
        const key = `${visualRowIdx}-${col}`;
        const isSelected = selectedCells.has(`${dataRowIdx}-${col}`);

        switch (col) {
            case 'Attribute': {
                const displayName = attrMap[row[col]] || row[col];
                return (
                    <td key={key} className={`px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 align-middle ${getColumnWidth()}`}>
                        <div className="flex items-center gap-2">
                            <div
                                className="block text-sm font-semibold leading-relaxed break-words cursor-pointer"
                                style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4', hyphens: 'auto' }}
                                onClick={() => handleRowToggleOrdered(visualRowIdx, row)}
                                title={displayName}
                            >
                                {displayName}
                            </div>
                        </div>
                    </td>
                );
            }

            case 'Walmart': {
                const isEditing = editingCell === dataRowIdx;
                const originalValue = cleanNaNValue(originalWalmartValues[dataRowIdx]);
                const currentValue = cleanNaNValue(editableWalmartValues[dataRowIdx]) || '-';
                const hasEdited = currentValue !== '-' && currentValue !== originalValue;
                const isEmpty = currentValue === '-';
                const hasData = !isEmpty;

                return (
                    <td
                        key={key}
                        className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer relative group hover:shadow-md transition-all duration-200 ${getColumnWidth()} 
                        ${isSelected ? 'ring-2 ring-blue-600 bg-blue-300 dark:bg-blue-700' : ''} 
                        ${hasEdited ? 'bg-yellow-100 dark:bg-yellow-900/50 ring-1 ring-yellow-400' : ''} 
                        ${!hasEdited && hasData ? getCellBackgroundColor(col, hasData) : ''} 
                        ${isEmpty ? 'bg-gray-50 dark:bg-gray-900 italic text-gray-500' : ''}`}
                        onClick={() => { if (!loading && !isEditing) handleCellToggleOrdered(visualRowIdx, col); }}
                    >
                        <div className="flex items-start gap-2">
                            <input
                                type="checkbox"
                                checked={isSelected}
                                readOnly
                                className="mt-1 h-4 w-4 rounded border border-blue-500 text-blue-700 focus:ring-2 focus:ring-blue-600 cursor-pointer flex-shrink-0"
                                onClick={(e) => { e.stopPropagation(); handleCellToggleOrdered(visualRowIdx, col); }}
                            />
                            <div className="relative flex-1 min-w-0">
                                {hasEdited && !isEditing && (
                                    <div className="absolute -top-3 -right-3 z-10">
                                        <div className="flex items-center gap-1 px-1 py-0.5 rounded-full bg-orange-500 text-white text-xs font-sans shadow-lg">
                                            <Edit2 size={8} />
                                            <span>EDITED</span>
                                        </div>
                                    </div>
                                )}

                                <button
                                    type="button"
                                    title="Edit Walmart value"
                                    onClick={(e) => { e.stopPropagation(); setEditingCell(dataRowIdx); }}
                                    className={`absolute -bottom-3 -right-1 p-1 rounded-full bg-white dark:bg-gray-800 border ${hasEdited ? 'border-orange-400 text-orange-600' : 'border-blue-300 dark:border-blue-600 opacity-0 group-hover:opacity-100'} hover:text-blue-600 hover:border-blue-500 hover:shadow-md transition-all duration-300 z-10`}
                                >
                                    <Edit2 size={12} className={hasEdited ? 'text-orange-500' : 'text-blue-500'} />
                                </button>

                                {!isEditing && (
                                    <div
                                        className={`block text-left font-medium text-sm leading-relaxed pr-6 break-words ${hasEdited ? 'text-orange-800 dark:text-orange-300 font-semibold' : ''} ${isEmpty ? 'italic text-gray-400 dark:text-gray-500' : ''}`}
                                        style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}
                                        title={currentValue}
                                    >
                                        {currentValue}
                                    </div>
                                )}

                                {isEditing && (
                                    <div className="space-y-2">
                                        <textarea
                                            value={currentValue === '-' ? '' : currentValue}
                                            autoFocus
                                            rows={4}
                                            onChange={(e) => {
                                                setEditableWalmartValues((prev: any) => ({ ...prev, [dataRowIdx]: e.target.value }));
                                            }}
                                            onBlur={() => saveEdit(dataRowIdx)}
                                            onKeyDown={(e) => {
                                                if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); saveEdit(dataRowIdx); }
                                                if (e.key === 'Escape') { cancelEdit(); }
                                            }}
                                            className="w-full border-2 border-orange-400 rounded px-2 py-1 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-orange-500 text-sm resize-none"
                                            placeholder="Enter Walmart value..."
                                        />
                                        {originalValue !== '-' && (
                                            <div className="text-xs text-gray-500 dark:text-gray-400">
                                                <div className="font-medium">Original:</div>
                                                <div className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs break-words">{originalValue}</div>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </td>
                );
            }

            case 'Walmart_Latest': {
                const walmartLatestValue = cleanNaNValue(row[col]);
                const isEmpty = walmartLatestValue === '-';
                const hasData = !isEmpty;
                const isValidUrl = isURL(walmartLatestValue) && !isEmpty;

                return (
                    <td
                        key={key}
                        className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer relative group hover:shadow-md transition-all duration-200 ${getColumnWidth()} 
                        ${isSelected ? 'ring-2 ring-teal-600 bg-teal-300 dark:bg-teal-700' : ''} 
                        ${hasData ? getCellBackgroundColor(col, hasData) : 'bg-gray-50 dark:bg-gray-900 italic text-gray-500'}`}
                        onClick={() => { if (!loading) handleCellToggleOrdered(visualRowIdx, col); }}
                    >
                        <div className="flex items-start gap-2">
                            <input
                                type="checkbox"
                                checked={isSelected}
                                readOnly
                                className="mt-1 h-4 w-4 rounded border border-teal-500 text-teal-700 focus:ring-2 focus:ring-teal-600 cursor-pointer flex-shrink-0"
                                onClick={(e) => { e.stopPropagation(); handleCellToggleOrdered(visualRowIdx, col); }}
                            />
                            <div className="flex-1 min-w-0">
                                {isValidUrl ? (
                                    <a
                                        href={walmartLatestValue}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-teal-700 dark:text-teal-300 hover:text-teal-900 dark:hover:text-teal-100 hover:underline transition-colors inline-flex items-center gap-1 group break-words"
                                        onClick={(e) => e.stopPropagation()}
                                        style={{
                                            overflowWrap: "anywhere",
                                            wordBreak: "break-word",
                                            whiteSpace: "normal",
                                            lineHeight: 1.4
                                        }}
                                    >
                                        <span className="flex-1 text-sm font-medium">{walmartLatestValue}</span>
                                        <ExternalLink className="w-3 h-3 opacity-60 group-hover:opacity-100 flex-shrink-0" />
                                    </a>
                                ) : (
                                    <div
                                        className={`block text-left font-medium text-sm leading-relaxed break-words ${isEmpty ? 'italic text-gray-500 dark:text-gray-400' : 'text-teal-800 dark:text-teal-200'}`}
                                        style={{
                                            wordBreak: 'break-word',
                                            overflowWrap: 'break-word',
                                            whiteSpace: 'normal',
                                            lineHeight: '1.4'
                                        }}
                                        title={walmartLatestValue}
                                    >
                                        {walmartLatestValue}
                                    </div>
                                )}
                            </div>
                        </div>
                    </td>
                );
            }

            case 'Validate Walmart': {
                const walmartConfig = getValidationConfig(walmartValidation[dataRowIdx] || '');
                const isWalmartSelected = isWalmartCellSelected(visualRowIdx);
                const hasData = Boolean(walmartValidation[dataRowIdx]);
                
                return (
                    <td key={key} className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle ${getCellBackgroundColor(col, hasData)} ${getColumnWidth()}`}>
                        <div className="flex flex-col items-center gap-2">
                            {walmartValidation[dataRowIdx] && (
                                <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${walmartConfig.bgColor} ${walmartConfig.color}`}>
                                    {walmartConfig.icon}
                                    <span className="text-xs font-semibold">{walmartConfig.label}</span>
                                </div>
                            )}
                            <div className="relative">
                                <select 
                                    value={walmartValidation[dataRowIdx] || ''} 
                                    disabled={!isWalmartSelected} 
                                    onChange={(e) => { 
                                        if (isWalmartSelected) { 
                                            setWalmartValidation((prev: any) => ({ ...prev, [dataRowIdx]: e.target.value })); 
                                        } 
                                    }} 
                                    className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${isWalmartSelected ? 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-blue-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500' : 'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'}`}
                                >
                                    <option value="">{isWalmartSelected ? 'Select...' : 'Select Walmart first'}</option>
                                    {isWalmartSelected && (
                                        <>
                                            <option value="Yes">✓ Yes</option>
                                            <option value="No">✗ No</option>
                                        </>
                                    )}
                                </select>
                                <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${isWalmartSelected ? 'text-gray-400' : 'text-gray-300'}`} />
                            </div>
                            {!isWalmartSelected && (
                                <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select Walmart cell to enable</div>
                            )}
                        </div>
                    </td>
                );
            }

            case 'Validate Walmart Latest': {
                const walmartLatestConfig = getValidationConfig(walmartLatestValidation[dataRowIdx] || '');
                const isWalmartLatestSelected = isWalmartLatestCellSelected(visualRowIdx);
                const hasData = Boolean(walmartLatestValidation[dataRowIdx]);

                return (
                    <td key={key} className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle ${getCellBackgroundColor(col, hasData)} ${getColumnWidth()}`}>
                        <div className="flex flex-col items-center gap-2">
                            {walmartLatestValidation[dataRowIdx] && (
                                <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${walmartLatestConfig.bgColor} ${walmartLatestConfig.color}`}>
                                    {walmartLatestConfig.icon}
                                    <span className="text-xs font-semibold">{walmartLatestConfig.label}</span>
                                </div>
                            )}
                            <div className="relative">
                                <select
                                    value={walmartLatestValidation[dataRowIdx] || ''}
                                    disabled={!isWalmartLatestSelected}
                                    onChange={(e) => {
                                        if (isWalmartLatestSelected) {
                                            setWalmartLatestValidation((prev: any) => ({
                                                ...prev,
                                                [dataRowIdx]: e.target.value
                                            }));
                                        }
                                    }}
                                    className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${isWalmartLatestSelected
                                            ? 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-teal-400 focus:ring-2 focus:ring-teal-500 focus:border-teal-500'
                                            : 'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                                        }`}
                                >
                                    <option value="">{isWalmartLatestSelected ? 'Select...' : 'Select Walmart Latest first'}</option>
                                    {isWalmartLatestSelected && (
                                        <>
                                            <option value="Yes">✓ Yes</option>
                                            <option value="No">✗ No</option>
                                        </>
                                    )}
                                </select>
                                <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${isWalmartLatestSelected ? 'text-gray-400' : 'text-gray-300'}`} />
                            </div>
                            {!isWalmartLatestSelected && (
                                <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select Walmart Latest cell to enable</div>
                            )}
                        </div>
                    </td>
                );
            }

            case 'Prefilled Value Comments': {
                const isWalmartSelectedForComment = isWalmartCellSelected(visualRowIdx);
                const hasData = Boolean(walmartComments[dataRowIdx]);
                
                return (
                    <td key={key} className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle ${getCellBackgroundColor(col, hasData)} ${getColumnWidth()}`}>
                        <div className="flex flex-col items-center gap-2">
                            {walmartComments[dataRowIdx] && (
                                <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                                    <MessageSquare className="w-3 h-3" />
                                    <span className="text-xs font-semibold">{walmartComments[dataRowIdx].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                                </div>
                            )}
                            <div className="relative">
                                <select 
                                    value={walmartComments[dataRowIdx] || ''} 
                                    disabled={!isWalmartSelectedForComment} 
                                    onChange={(e) => { 
                                        if (isWalmartSelectedForComment) { 
                                            setWalmartComments((prev: any) => ({ ...prev, [dataRowIdx]: e.target.value })); 
                                        } 
                                    }} 
                                    className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${isWalmartSelectedForComment ? 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-blue-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500' : 'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'}`}
                                >
                                    <option value="">{isWalmartSelectedForComment ? 'Select...' : 'Select Walmart first'}</option>
                                    {isWalmartSelectedForComment && (
                                        <>
                                            <option value="curated-from-base">Curated from base data</option>
                                            <option value="curated-from-walmart-latest">Curated from Walmart Latest</option>
                                            <option value="walmart-has-different-value"> Walmart has different value </option>
                                            <option value="vpd">VPD</option>
                                            <option value="validated">Validated</option>
                                            <option value="unable-to-curate">Unable to curate</option>
                                            <option value="value-not-provided">Value not provided</option>
                                            <option value="not-acceptable-value">Not in acceptable value</option>
                                        </>
                                    )}
                                </select>
                                <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${isWalmartSelectedForComment ? 'text-gray-400' : 'text-gray-300'}`} />
                            </div>
                            {!isWalmartSelectedForComment && (
                                <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select Walmart cell to enable</div>
                            )}
                        </div>
                    </td>
                );
            }

            case 'Walmart Latest Comments': {
                const isWalmartLatestSelectedForComment = isWalmartLatestCellSelected(visualRowIdx);
                const hasData = Boolean(walmartLatestComments[dataRowIdx]);

                return (
                    <td key={key} className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle ${getCellBackgroundColor(col, hasData)} ${getColumnWidth()}`}>
                        <div className="flex flex-col items-center gap-2">
                            {walmartLatestComments[dataRowIdx] && (
                                <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-teal-100 text-teal-800">
                                    <MessageSquare className="w-3 h-3" />
                                    <span className="text-xs font-semibold">
                                        {walmartLatestComments[dataRowIdx].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                    </span>
                                </div>
                            )}
                            <div className="relative">
                                <select
                                    value={walmartLatestComments[dataRowIdx] || ''}
                                    disabled={!isWalmartLatestSelectedForComment}
                                    onChange={(e) => {
                                        if (isWalmartLatestSelectedForComment) {
                                            setWalmartLatestComments((prev: any) => ({
                                                ...prev,
                                                [dataRowIdx]: e.target.value
                                            }));
                                        }
                                    }}
                                    className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${isWalmartLatestSelectedForComment
                                            ? 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-teal-400 focus:ring-2 focus:ring-teal-500 focus:border-teal-500'
                                            : 'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                                        }`}
                                >
                                    <option value="">{isWalmartLatestSelectedForComment ? 'Select...' : 'Select Walmart Latest first'}</option>
                                    {isWalmartLatestSelectedForComment && (
                                        <>
                                            <option value="curated-from-latest">Curated from latest data</option>
                                            <option value="latest-vpd">Latest VPD</option>
                                            <option value="validated-latest">Validated Latest</option>
                                            <option value="unable-to-curate-latest">Unable to curate latest</option>
                                            <option value="latest-value-not-provided">Latest value not provided</option>
                                            <option value="not-acceptable-latest-value">Not acceptable latest value</option>
                                        </>
                                    )}
                                </select>
                                <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${isWalmartLatestSelectedForComment ? 'text-gray-400' : 'text-gray-300'}`} />
                            </div>
                            {!isWalmartLatestSelectedForComment && (
                                <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select Walmart Latest cell to enable</div>
                            )}
                        </div>
                    </td>
                );
            }

            case 'LLM': {
                const llmValue = cleanNaNValue(llmValues[dataRowIdx]);
                const isEmpty = llmValue === '-';
                const hasData = !isEmpty;
                let cellSourceUrl = '';
                
                try {
                    const parsedData = JSON.parse(rawJson || '{}');
                    const currentAttribute = tableRows[dataRowIdx]?.Attribute;
                    const llmSourceData = parsedData.llm_suggested?.[currentAttribute];
                    cellSourceUrl = llmSourceData?.source_url || '';
                } catch (e) {
                    // ignore
                }

                const hasValidData = llmValue && llmValue !== '-' && cellSourceUrl && cellSourceUrl !== '-' && cellSourceUrl !== 'null';

                return (
                    <td key={key} className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer hover:shadow-md transition-all duration-200 ${getColumnWidth()} 
                    ${isLlmCellSelected(visualRowIdx) ? 'ring-2 ring-purple-600 bg-purple-300 dark:bg-purple-700' : ''} 
                    ${hasData ? getCellBackgroundColor(col, hasData) : 'bg-gray-50 dark:bg-gray-900'}`} 
                    onClick={() => handleCellToggleOrdered(visualRowIdx, 'LLM')} 
                    title={`${llmValue}${hasValidData ? ` - Click to visit source: ${cellSourceUrl}` : ''}`}>
                        <div className="flex items-start gap-2">
                            <input 
                                type="checkbox" 
                                checked={isLlmCellSelected(visualRowIdx)} 
                                readOnly 
                                className="mt-1 h-4 w-4 rounded border border-purple-500 text-purple-700 focus:ring-2 focus:ring-purple-600 cursor-pointer flex-shrink-0" 
                                onClick={(e) => { e.stopPropagation(); handleCellToggleOrdered(visualRowIdx, 'LLM'); }} 
                            />
                            <div className="flex-1 min-w-0">
                                {hasValidData ? (
                                    <button 
                                        onClick={(e) => { 
                                            e.stopPropagation(); 
                                            const a = document.createElement('a'); 
                                            a.href = cellSourceUrl; 
                                            a.target = '_blank'; 
                                            a.rel = 'noopener noreferrer'; 
                                            a.click(); 
                                        }} 
                                        className="block text-left font-medium text-sm leading-relaxed break-words text-purple-700 dark:text-purple-300 hover:text-purple-900 dark:hover:text-purple-100 hover:underline transition-colors w-full group" 
                                        style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }} 
                                        title={`Click to visit source: ${cellSourceUrl}`}
                                    >
                                        <div className="flex items-center gap-1 justify-between">
                                            <span className="flex-1">{llmValue}</span>
                                            <ExternalLink className="w-3 h-3 opacity-60 group-hover:opacity-100 flex-shrink-0" />
                                        </div>
                                    </button>
                                ) : (
                                    <div className={`block text-left font-medium text-sm leading-relaxed break-words ${isEmpty ? 'italic text-gray-500 dark:text-gray-400' : ''}`} style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}>
                                        {llmValue}
                                    </div>
                                )}
                            </div>
                        </div>
                    </td>
                );
            }

            case 'Validate LLM': {
                const llmConfig = getValidationConfig(llmValidation[dataRowIdx] || '');
                const isLlmSelected = isLlmCellSelected(visualRowIdx);
                const hasData = Boolean(llmValidation[dataRowIdx]);
                
                return (
                    <td key={key} className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle ${getCellBackgroundColor(col, hasData)} ${getColumnWidth()}`}>
                        <div className="flex flex-col items-center gap-2">
                            {llmValidation[dataRowIdx] && (
                                <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${llmConfig.bgColor} ${llmConfig.color}`}>
                                    {llmConfig.icon}
                                    <span className="text-xs font-semibold">{llmConfig.label}</span>
                                </div>
                            )}
                            <div className="relative">
                                <select 
                                    value={llmValidation[dataRowIdx] || ''} 
                                    disabled={!isLlmSelected} 
                                    onChange={(e) => { 
                                        if (isLlmSelected) { 
                                            setLlmValidation((prev: any) => ({ ...prev, [dataRowIdx]: e.target.value })); 
                                        } 
                                    }} 
                                    className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${isLlmSelected ? 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-purple-400 focus:ring-2 focus:ring-purple-500 focus:border-purple-500' : 'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'}`}
                                >
                                    <option value="">{isLlmSelected ? 'Select...' : 'Select LLM first'}</option>
                                    {isLlmSelected && (
                                        <>
                                            <option value="Yes">✓ Yes</option>
                                            <option value="No">✗ No</option>
                                        </>
                                    )}
                                </select>
                                <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${isLlmSelected ? 'text-gray-400' : 'text-gray-300'}`} />
                            </div>
                            {!isLlmSelected && (
                                <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select LLM cell to enable</div>
                            )}
                        </div>
                    </td>
                );
            }

            case 'LLM Comment': {
                const isLlmSelectedForComment = isLlmCellSelected(visualRowIdx);
                const hasData = Boolean(llmComments[dataRowIdx]);
                
                return (
                    <td key={key} className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle ${getCellBackgroundColor(col, hasData)} ${getColumnWidth()}`}>
                        <div className="flex flex-col items-center gap-2">
                            {llmComments[dataRowIdx] && (
                                <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-purple-100 text-purple-800">
                                    <MessageSquare className="w-3 h-3" />
                                    <span className="text-xs font-semibold">{llmComments[dataRowIdx].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                                </div>
                            )}
                            <div className="relative">
                                <select 
                                    value={llmComments[dataRowIdx] || ''} 
                                    disabled={!isLlmSelectedForComment} 
                                    onChange={(e) => { 
                                        if (isLlmSelectedForComment) { 
                                            setLlmComments((prev: any) => ({ ...prev, [dataRowIdx]: e.target.value })); 
                                        } 
                                    }} 
                                    className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${isLlmSelectedForComment ? 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-purple-400 focus:ring-2 focus:ring-purple-500 focus:border-purple-500' : 'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'}`}
                                >
                                    <option value="">{isLlmSelectedForComment ? 'Select...' : 'Select LLM first'}</option>
                                    {isLlmSelectedForComment && (
                                        <>
                                            <option value="value-found-from-llm">Value found from LLM</option>
                                            <option value="llm-value-mismatch">LLM Value Mismatch</option>
                                            <option value="no-value-found">No Value Found</option>
                                        </>
                                    )}
                                </select>
                                <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${isLlmSelectedForComment ? 'text-gray-400' : 'text-gray-300'}`} />
                            </div>
                            {!isLlmSelectedForComment && (
                                <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select LLM cell to enable</div>
                            )}
                        </div>
                    </td>
                );
            }

            case 'Final Value': {
                const hasData = Boolean(finalValues[dataRowIdx]?.finalValue);
                return (
                    <td key={key} className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 text-center align-middle ${getCellBackgroundColor(col, hasData)} font-medium ${getColumnWidth()}`}>
                        <div className="block text-sm break-words" style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }} title={finalValues[dataRowIdx]?.finalValue || '-'}>
                            {finalValues[dataRowIdx]?.finalValue || '-'}
                        </div>
                    </td>
                );
            }

            case 'Source': {
                const raw = finalValues[dataRowIdx]?.source ?? finalValues[dataRowIdx]?.source ?? '';
                const safeUrl = getSafeHttpUrl(raw);
                const display = safeUrl ?? (raw || '-');
                const hasData = Boolean(raw);

                return (
                    <td
                        key={key}
                        className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 text-center align-middle ${getCellBackgroundColor(col, hasData)} font-medium ${getColumnWidth()}`}
                    >
                        {safeUrl ? (
                            <a
                                href={safeUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="block text-sm text-blue-700 underline break-all"
                                style={{ overflowWrap: 'anywhere', wordBreak: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}
                                title={display}
                            >
                                {display}
                            </a>
                        ) : (
                            <div
                                className="block text-sm break-words"
                                style={{ overflowWrap: 'anywhere', wordBreak: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}
                                title={display}
                            >
                                {display}
                            </div>
                        )}
                    </td>
                );
            }

            case 'Final Validation': {
                const walmartValidationVal = walmartValidation[dataRowIdx] || '';
                const walmartLatestValidationVal = walmartLatestValidation[dataRowIdx] || '';
                const llmValidationVal = llmValidation[dataRowIdx] || '';
                const finalValidationDisplay = [walmartValidationVal, walmartLatestValidationVal, llmValidationVal]
                    .filter(v => v !== '').join(', ') || '-';
                const hasData = finalValidationDisplay !== '-';

                return (
                    <td key={key} className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 text-center align-middle ${getCellBackgroundColor(col, hasData)} font-medium ${getColumnWidth()}`}>
                        <div className="block text-sm break-words" style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }} title={finalValidationDisplay}>
                            {finalValidationDisplay}
                        </div>
                    </td>
                );
            }

            case 'Final Comment': {
                const walmartCommentVal = walmartComments[dataRowIdx] || '';
                const walmartLatestCommentVal = walmartLatestComments[dataRowIdx] || '';
                const llmCommentVal = llmComments[dataRowIdx] || '';

                const formatComment = (comment: string) => {
                    if (!comment) return '';
                    return comment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                };

                const formattedComments = [
                    formatComment(walmartCommentVal),
                    formatComment(walmartLatestCommentVal),
                    formatComment(llmCommentVal)
                ].filter(v => v !== '').join(' | ') || '-';
                const hasData = formattedComments !== '-';

                return (
                    <td key={key} className={`border border-gray-300 px-3 py-3 dark:text-gray-200 text-center align-middle ${getCellBackgroundColor(col, hasData)} font-medium ${getColumnWidth()}`}>
                        <div className="block text-sm break-words" style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }} title={formattedComments}>
                            {formattedComments}
                        </div>
                    </td>
                );
            }

            case 'Final Verdict': {
                const isEditing = editingFinalVerdict === dataRowIdx;
                const currentVerdict = finalVerdictsState[dataRowIdx] || '';
                const autoVerdict = generateAutoFinalVerdict(dataRowIdx);
                const isEmpty = currentVerdict === '';
                const isManuallyEdited = currentVerdict.includes("MANUAL:");
                const hasData = !isEmpty;

                // Clean manual content: remove Value, Validation, and platform icon only (keep name)
                const cleanManualContent = currentVerdict
                    .replace("MANUAL: ", "")
                    .split("\n")
                    .filter(line => {
                        const lower = line.toLowerCase();
                        // Remove Value and Validation lines
                        return !lower.startsWith("value:") && !lower.startsWith("validation:");
                    })
                    .map(line => {
                        // Remove only the platform icon (🛒), keep platform name
                        return line.replace(/^🛒\s*/, '');
                    })
                    .join("\n");

                return (
                    <td
                        key={key}
                        className={`border dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer relative group hover:shadow-md transition-all duration-200 ${getColumnWidth()} ${getCellBackgroundColor(col, hasData)}`}
                    >
                        <div className="flex flex-col gap-2">
                            <div className="flex items-center justify-between">
                                <span className="text-xs font-semibold text-violet-700 dark:text-violet-300">Final Verdict</span>
                                <div className="flex gap-1">
                                    {!isEmpty && (
                                        <button
                                            type="button"
                                            onClick={() => setEditingFinalVerdict(dataRowIdx)}
                                            className="p-1 rounded bg-violet-200 dark:bg-violet-800 hover:bg-violet-300 dark:hover:bg-violet-700 transition-colors"
                                            title="Edit Comment & Source"
                                        >
                                            <Edit2 size={12} className="text-violet-600 dark:text-violet-300" />
                                        </button>
                                    )}
                                </div>
                            </div>

                            <div className="text-xs">
                                {isManuallyEdited && (
                                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                        Edited
                                    </span>
                                )}
                            </div>

                            {!isEditing && (
                                <div
                                    className={`text-sm leading-relaxed break-words ${isEmpty ? 'italic text-gray-400 dark:text-gray-500' : 'text-violet-800 dark:text-violet-200'} cursor-pointer max-h-32 overflow-y-auto`}
                                    style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'pre-wrap', lineHeight: '1.4' }}
                                    onClick={() => setEditingFinalVerdict(dataRowIdx)}
                                    title={cleanManualContent || "Click to add comment and source"}
                                >
                                    {cleanManualContent || "Click to add comment and source..."}
                                </div>
                            )}

                            {isEditing && (
                                <div className="space-y-2">
                                    <textarea
                                        value={cleanManualContent}
                                        autoFocus
                                        rows={4}
                                        onChange={(e) => {
                                            setFinalVerdictsState((prev: any) => ({
                                                ...prev,
                                                [dataRowIdx]: `MANUAL: ${e.target.value}`
                                            }));
                                        }}
                                        onBlur={() => setEditingFinalVerdict(null)}
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter' && e.ctrlKey) {
                                                e.preventDefault();
                                                setEditingFinalVerdict(null);
                                            }
                                            if (e.key === 'Escape') {
                                                setEditingFinalVerdict(null);
                                            }
                                        }}
                                        className="w-full border-2 border-violet-400 rounded px-2 py-1 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-violet-500 text-sm resize-none"
                                        placeholder="Enter comment and source only..."
                                    />
                                    <div className="text-xs text-gray-500 dark:text-gray-400">
                                        <div className="font-medium">Auto-generated suggestion:</div>
                                        <div className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs break-words max-h-24 overflow-y-auto">
                                            {autoVerdict || "Select data to see auto-generated verdict"}
                                        </div>
                                        <div className="mt-1 text-xs text-gray-400">
                                            Ctrl+Enter to save, Escape to cancel • Only comment & source are editable
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </td>
                );
            }

            default: {
                const cellValue = cleanNaNValue(row[col]);
                const isEmpty = cellValue === '-';
                const hasData = !isEmpty;
                
                return (
                    <td key={key} className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer hover:shadow-md transition-all duration-200 ${getColumnWidth()} 
                    ${isSelected ? 'ring-2 ring-blue-400 bg-blue-100 dark:bg-blue-900' : ''} 
                    ${getCellBackgroundColor(col, hasData)}`} 
                    onClick={() => handleCellToggleOrdered(visualRowIdx, col)} 
                    title={cellValue}>
                        <div className="flex items-start gap-2">
                            <input type="checkbox" checked={isSelected} readOnly className="mt-1 h-4 w-4 rounded border border-gray-400 text-blue-600 focus:ring-2 focus:ring-blue-500 flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                                {isURL(cellValue) && cellValue !== '-' ? (
                                    <a href={cellValue} target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline transition-colors  items-center gap-1 group text-sm break-words block" onClick={(e) => e.stopPropagation()} style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}>
                                        <span className="flex-1">{cellValue}</span>
                                        <ExternalLink className="w-3 h-3 opacity-60 group-hover:opacity-100 flex-shrink-0" />
                                    </a>
                                ) : (
                                    <div className={`block text-left text-sm leading-relaxed font-medium break-words ${isEmpty ? 'italic text-gray-400 dark:text-gray-500' : ''}`} style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}>
                                        {cellValue}
                                    </div>
                                )}
                            </div>
                        </div>
                    </td>
                );
            }
        }
    };

    return (
        <div>
            <div className="overflow-auto max-h-[70vh] rounded-2xl border border-gray-300 dark:border-gray-600 shadow-xl bg-white dark:bg-gray-900 mb-6">
                <table className="text-sm table-auto min-w-[2200px] border-collapse">
                    <thead>
                        <tr>
                            {getTableHeaders().map((col) => {
                                const colWidth = getColumnWidth();
                                const isCompetitor = !['Attribute', 'Walmart', 'Walmart_Latest', 'LLM', 'Final Validation', 'Final Comment', 'Final Value', 'Source', 'Final Verdict'].includes(col) && !col.includes('Validate') && !col.includes('Comment');

                                return (
                                    <th
                                        key={col}
                                        className={`px-3 py-3 text-xs font-bold uppercase tracking-wide 
                                        text-white border border-gray-400 dark:border-gray-500 
                                        text-center shadow-sm ${colWidth} sticky top-0 z-30 
                                        ${getHeaderColor(col)} ${col === 'Attribute' ? 'left-0 z-40' : ''}`}
                                    >
                                        {col === 'LLM' && (
                                            <div className="flex items-center justify-center gap-1">
                                                <Sparkles className="w-4 h-4 text-white" />
                                                <span className="text-sm font-bold">LLM</span>
                                            </div>
                                        )}
                                        {col === 'Walmart_Latest' && (
                                            <span className="text-sm font-bold break-words" title="Walmart Latest">
                                                Walmart Latest
                                            </span>
                                        )}
                                        {col === 'Final Verdict' && (
                                            <div className="font-extrabold text-center break-words px-2">
                                                <div className=" rounded-lg px-2 py-1 text-white">Final Verdict</div>
                                            </div>
                                        )}
                                        {col === 'Validate Walmart Latest' && (
                                            <div className="flex items-center justify-center gap-1">
                                                <CheckSquare className="w-4 h-4 text-white" />
                                                <span className="font-extrabold">Validate Latest</span>
                                            </div>
                                        )}
                                        {col === 'Walmart Latest Comments' && (
                                            <div className="flex items-center justify-center gap-1">
                                                <MessageSquare className="w-4 h-4 text-white" />
                                                <span className="font-extrabold">Latest Comments</span>
                                            </div>
                                        )}
                                        {col.startsWith('Validate') && col !== 'Validate Walmart Latest' ? (
                                            <div className="flex items-center justify-center gap-1">
                                                <CheckSquare className="w-4 h-4 text-white" />
                                                <span className="font-extrabold">Validate</span>
                                            </div>
                                        ) : col !== 'LLM' &&
                                            col !== 'Walmart_Latest' &&
                                            col !== 'Final Verdict' &&
                                            col !== 'Validate Walmart Latest' &&
                                            col !== 'Walmart Latest Comments' ? (
                                            col === 'Brand' ? (
                                                <span className="text-sm font-bold break-words">Brand</span>
                                            ) : isCompetitor ? renderCompetitorHeader(col) : (
                                                <div className="font-extrabold text-center break-words px-2">
                                                    {col.includes('Final') ? (
                                                        <div className=" rounded-lg px-2 py-1">{col}</div>
                                                    ) : (
                                                        col
                                                    )}
                                                </div>
                                            )
                                        ) : null}
                                    </th>
                                );
                            })}
                        </tr>
                    </thead>
                    <tbody>
                        {getOrderedTableRows(tableRows).map((row, visualRowIdx) => (
                            <tr key={visualRowIdx} className={`${visualRowIdx % 2 === 0 ? 'bg-gray-50 dark:bg-gray-850' : 'bg-white dark:bg-gray-900'} hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors`}>
                                {getTableHeaders().map((col, colIdx) => {
                                    if (col === 'Attribute') {
                                        const displayName = attrMap[row[col]] || row[col];
                                        const stickyBg = visualRowIdx % 2 === 0 ? 'bg-gray-50 dark:bg-gray-850' : 'bg-white dark:bg-gray-900';
                                        return (
                                            <td key={`${visualRowIdx}-${col}-${colIdx}`} className={`px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 align-middle sticky left-0 z-20 ${stickyBg} font-semibold ${getColumnWidth()}`}>
                                                <div className="flex items-center gap-2">
                                                    <div
                                                        className="block text-sm font-semibold leading-relaxed break-words cursor-pointer"
                                                        style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4', hyphens: 'auto' }}
                                                        onClick={() => handleRowToggleOrdered(visualRowIdx, row)}
                                                        title={displayName}
                                                    >
                                                        {displayName}
                                                    </div>
                                                </div>
                                            </td>
                                        );
                                    } else {
                                        return renderTableCell(col, visualRowIdx, row);
                                    }
                                })}
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            <div className="flex items-center justify-end gap-3 mt-4">
                <button
                    onClick={() => exportToWideFormatCSV && exportToWideFormatCSV()}
                    disabled={loading || (tableRows.length === 0)}
                    className={`inline-flex items-center gap-2 px-4 py-2 rounded-full font-semibold shadow-lg transition-transform transform hover:-translate-y-0.5 ${loading || tableRows.length === 0 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white'}`}
                    title="Export current table to wide-format CSV"
                >
                    <svg className="w-4 h-4 opacity-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v4a1 1 0 001 1h3m10 0h3a1 1 0 001-1V7M8 21h8M12 7v14" />
                    </svg>
                    <span>Export CSV</span>
                </button>

                <button
                    onClick={() => generateInsights && generateInsights()}
                    disabled={loading || insightsLoading || (tableRows.length === 0)}
                    className={`inline-flex items-center gap-2 px-4 py-2 rounded-full font-semibold shadow-2xl transition-transform transform hover:-translate-y-0.5 ${loading || insightsLoading || tableRows.length === 0 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-gradient-to-r from-emerald-500 to-teal-500 text-white'}`}
                    title="Generate product insights from selected data"
                >
                    <Sparkles className={`w-4 h-4 ${insightsLoading ? 'animate-spin' : ''}`} />
                    <span>{insightsLoading ? 'Generating…' : 'Generate Insights'}</span>
                </button>
            </div>
        </div>
    );
};

export default ResponseTable;

function getSafeHttpUrl(raw: string): string | null {
    if (!raw || typeof raw !== 'string') return null;
    try {
        const url = new URL(raw);
        if (url.protocol === 'http:' || url.protocol === 'https:') {
            return url.href;
        }
        return null;
    } catch {
        return null;
    }
}
