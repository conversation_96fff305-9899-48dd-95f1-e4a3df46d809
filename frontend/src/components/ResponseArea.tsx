import { useState, useEffect, useRef, useCallback } from "react";
import { Loader2 } from "lucide-react";
import FeedbackForm from "./FeedbackForm";
import { useSelectionStore } from "../store/selectionStore";
import ResponseTable from "./ResponseTable";
import ResponseControls from "./ResponseControls";
import { marked } from "marked";
import { processLlmValue } from "@/lib/utils";
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

type WalmartRow = { id_str?: string;[key: string]: any };

interface ResponseAreaProps {
  preloadedRow: WalmartRow
}

interface EnhancedFinalValue {
  Attribute?: string;
  finalValue: string;
  source: string;
  walmartValidation?: string;
  llmValidation?: string;
  walmartComment?: string;
  llmComment?: string;
  finalValidation?: string;
  finalComment?: string;
  finalVerdict?: string; // ✅ NEW
  rawData?: any
}

export default function ResponseArea({ preloadedRow }: ResponseAreaProps) {
  const {
    selections,
    editableValues,
    validations,
    comments,
    finalVerdicts, // ✅ NEW
    setSelections,
    clearSelections,
    setEditableValues,
    setValidations,
    setComments,
    setFinalVerdicts // ✅ NEW
  } = useSelectionStore();

  const [loading, setLoading] = useState(false);
  const [tableRows, setTableRows] = useState<Record<string, string>[]>([]);
  const [rawJson, setRawJson] = useState("");
  const [shortSummary, setShortSummary] = useState("");
  const [detailedSummary, setDetailedSummary] = useState("");
  const [insightsGenerated, setInsightsGenerated] = useState(false);
  const [insightsLoading, setInsightsLoading] = useState(false);
  const [attrMap, setAttrMap] = useState<Record<string, string>>({});
  const [competitorUrls, setCompetitorUrls] = useState<Record<string, string>>({});
  const [brandUrl, setBrandUrl] = useState("");
  const [llmSourceUrl, setLlmSourceUrl] = useState("");

  const [selectedCells, setSelectedCells] = useState<Set<string>>(new Set());
  const [editableWalmartValues, setEditableWalmartValues] = useState<Record<number, string>>({});
  const [originalWalmartValues, setOriginalWalmartValues] = useState<Record<number, string>>({});
  const [llmValues, setLlmValues] = useState<Record<number, string>>({});
  const [editingCell, setEditingCell] = useState<number | null>(null);

  const [walmartValidation, setWalmartValidation] = useState<Record<number, string>>({});
  const [llmValidation, setLlmValidation] = useState<Record<number, string>>({});
  const [walmartComments, setWalmartComments] = useState<Record<number, string>>({});
  const [llmComments, setLlmComments] = useState<Record<number, string>>({});

  // ✅ NEW: Add Walmart Latest validation and comments state
  const [walmartLatestValidation, setWalmartLatestValidation] = useState<Record<number, string>>({});
  const [walmartLatestComments, setWalmartLatestComments] = useState<Record<number, string>>({});

  const [finalVerdictsState, setFinalVerdictsState] = useState<Record<number, string>>({}); // ✅ NEW
  const [editingFinalVerdict, setEditingFinalVerdict] = useState<number | null>(null); // ✅ NEW
  const [detailedSummaryEditMode, setDetailedSummaryEditMode] = useState(false); // ...new code...

  const [detailedSummaryRaw, setDetailedSummaryRaw] = useState(""); // Store raw markdown  ...new code...



  const currentId = preloadedRow?.id_str || '';

  const loadedRef = useRef(false);
  const lastIdRef = useRef('');
  const initialLoadRef = useRef(false); // ✅ NEW: Track if initial load happened

  // ✅ Load saved state ONLY when ID changes
  useEffect(() => {
    if (currentId && currentId !== lastIdRef.current) {
      lastIdRef.current = currentId;
      loadedRef.current = false;

      const savedSelections = selections[currentId] || [];
      setSelectedCells(new Set(savedSelections));

      const savedEditableValues = editableValues[currentId] || {};
      setEditableWalmartValues(savedEditableValues);

      const savedValidations = validations[currentId] || { walmart: {}, llm: {}, walmartLatest: {} };
      setWalmartValidation(savedValidations.walmart || {});
      setLlmValidation(savedValidations.llm || {});
      setWalmartLatestValidation(savedValidations.walmartLatest || {}); // ✅ NEW

      const savedComments = comments[currentId] || { walmart: {}, llm: {}, walmartLatest: {} };
      setWalmartComments(savedComments.walmart || {});
      setLlmComments(savedComments.llm || {});
      setWalmartLatestComments(savedComments.walmartLatest || {}); // ✅ NEW

      // ✅ NEW: Load final verdicts
      const savedFinalVerdicts = finalVerdicts[currentId] || {};
      setFinalVerdictsState(savedFinalVerdicts);

      loadedRef.current = true;
    }
  }, [currentId, selections, editableValues, validations, comments, finalVerdicts]);

  useEffect(() => {

    if (preloadedRow?.id_str && !initialLoadRef.current) {
      initialLoadRef.current = true;
      fetchResponse();
    }
  }, [preloadedRow?.id_str]);

  useEffect(() => {
    if (preloadedRow?.id_str &&
      preloadedRow.id_str !== lastIdRef.current &&
      initialLoadRef.current) {
      fetchResponse();
    }
  }, [preloadedRow?.id_str]);

  const saveSelections = useCallback((cells: Set<string>) => {
    if (currentId && loadedRef.current) {
      setSelections(currentId, Array.from(cells));
    }
  }, [currentId, setSelections]);

  const saveEditableValues = useCallback((values: Record<number, string>) => {
    if (currentId && loadedRef.current && Object.keys(values).length > 0) {
      setEditableValues(currentId, values);
    }
  }, [currentId, setEditableValues]);

  const saveWalmartValidation = useCallback((validation: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setValidations(currentId, 'walmart', validation);
    }
  }, [currentId, setValidations]);

  const saveLlmValidation = useCallback((validation: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setValidations(currentId, 'llm', validation);
    }
  }, [currentId, setValidations]);

  // ✅ NEW: Save Walmart Latest validation
  const saveWalmartLatestValidation = useCallback((validation: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setValidations(currentId, 'walmartLatest', validation);
    }
  }, [currentId, setValidations]);

  const saveWalmartComments = useCallback((comments: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setComments(currentId, 'walmart', comments);
    }
  }, [currentId, setComments]);

  const saveLlmComments = useCallback((comments: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setComments(currentId, 'llm', comments);
    }
  }, [currentId, setComments]);

  // ✅ NEW: Save Walmart Latest comments
  const saveWalmartLatestComments = useCallback((comments: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setComments(currentId, 'walmartLatest', comments);
    }
  }, [currentId, setComments]);

  const saveFinalVerdicts = useCallback((verdicts: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setFinalVerdicts(currentId, verdicts);
    }
  }, [currentId, setFinalVerdicts]);

  // ✅ Auto-save effects
  useEffect(() => {
    saveSelections(selectedCells);
  }, [selectedCells, saveSelections]);

  useEffect(() => {
    saveEditableValues(editableWalmartValues);
  }, [editableWalmartValues, saveEditableValues]);

  useEffect(() => {
    saveWalmartValidation(walmartValidation);
  }, [walmartValidation, saveWalmartValidation]);

  useEffect(() => {
    saveLlmValidation(llmValidation);
  }, [llmValidation, saveLlmValidation]);

  // ✅ NEW: Auto-save Walmart Latest validation
  useEffect(() => {
    saveWalmartLatestValidation(walmartLatestValidation);
  }, [walmartLatestValidation, saveWalmartLatestValidation]);

  useEffect(() => {
    saveWalmartComments(walmartComments);
  }, [walmartComments, saveWalmartComments]);

  useEffect(() => {
    saveLlmComments(llmComments);
  }, [llmComments, saveLlmComments]);

  // ✅ NEW: Auto-save Walmart Latest comments
  useEffect(() => {
    saveWalmartLatestComments(walmartLatestComments);
  }, [walmartLatestComments, saveWalmartLatestComments]);

  useEffect(() => {
    saveFinalVerdicts(finalVerdictsState);
  }, [finalVerdictsState, saveFinalVerdicts]);

  const cleanNaNValue = (v: any): string => {
    if (v === null || v === undefined) return "-";
    if (typeof v === "string") {
      const s = v.trim();
      if (s === "" || s.toLowerCase() === "nan") return "-";
      return s;
    }
    return String(v);
  };

  // ✅ FIXED: Explicitly typed generateAutoFinalVerdict function
const generateAutoFinalVerdict = useCallback((rowIdx: number): string => {
  const row = tableRows[rowIdx];
  if (!row) return "";

  const selectedValues: string[] = [];

  const hasWalmartSelection = selectedCells.has(`${rowIdx}-Walmart`);
  const hasLlmSelection = selectedCells.has(`${rowIdx}-LLM`);
  const hasWalmartLatestSelection = selectedCells.has(`${rowIdx}-Walmart_Latest`);

  // ✅ Walmart section (detailed with URL and comment)
  if (hasWalmartSelection) {
    const walmartValue = cleanNaNValue(editableWalmartValues[rowIdx] || row["Walmart"]);
    const walmartCommentValue = walmartComments[rowIdx] || "";
  //  const walmartUrl = competitorUrls["Walmart"] || "";

    const walmartParts: string[] = [];
    walmartParts.push(`Value: ${walmartValue}`);

  //  if (walmartUrl && walmartUrl !== "-") {
   //   walmartParts.push(`Source: ${walmartUrl}`);
   // }

    if (walmartCommentValue) {
      const formattedComment = walmartCommentValue
        .replace(/-/g, " ")
        .replace(/\b\w/g, (l) => l.toUpperCase());
      walmartParts.push(`Comment: ${formattedComment}`);
    }

    selectedValues.push(`\n${walmartParts.join("\n")}`);
  }

  // ✅ Walmart Latest section (ONLY VALUE - no URL, no comment)
  if (hasWalmartLatestSelection) {
    const walmartLatestValue = cleanNaNValue(row["Walmart_Latest"]);
    if (walmartLatestValue !== "-") {
      // Just push the raw value, no "Value:" prefix, no URL, no comment
      selectedValues.push(walmartLatestValue);
    }
  }

  // ✅ LLM section (detailed with URL and comment)
  if (hasLlmSelection) {
    const llmValue = cleanNaNValue(llmValues[rowIdx]);
    const llmCommentValue = llmComments[rowIdx] || "";
    const llmUrl = llmSourceUrl || "";

    // ✅ FIXED: Synchronous JSON parsing without async/await
    let specificLlmUrl = llmUrl;
    try {
      const parsedData = JSON.parse(rawJson || "{}");
      const currentAttribute = tableRows[rowIdx]?.Attribute;
      const llmSourceData = parsedData.llm_suggested?.[currentAttribute];
      if (llmSourceData?.source_url) {
        specificLlmUrl = llmSourceData.source_url;
      }
    } catch (parseError) {
      // Silently use default llmUrl if parsing fails
      specificLlmUrl = llmUrl;
    }

    const llmParts: string[] = [];
    llmParts.push(`Value: ${llmValue}`);

    if (specificLlmUrl && specificLlmUrl !== "-" && specificLlmUrl !== "null") {
      llmParts.push(`Source: ${specificLlmUrl}`);
    }

    if (llmCommentValue) {
      const formattedComment = llmCommentValue
        .replace(/-/g, " ")
        .replace(/\b\w/g, (l) => l.toUpperCase());
      llmParts.push(`Comment: ${formattedComment}`);
    }

    selectedValues.push(`\n${llmParts.join("\n")}`);
  }

  // ✅ FIXED: Ensure synchronous string return
  if (selectedValues.length === 0) {
    return "No data selected for this attribute";
  }

  return selectedValues.join(", ");
}, [
  tableRows,
  selectedCells,
  editableWalmartValues,
  llmValues,
  walmartComments,
  llmComments,
  competitorUrls,
  llmSourceUrl,
  rawJson
]);



  // ✅ Auto-update final verdicts
  useEffect(() => {
    if (currentId && loadedRef.current && tableRows.length > 0) {
      const updatedVerdicts: Record<number, string> = {};
      let hasChanges = false;

      tableRows.forEach((_, rowIdx) => {
        const newVerdict = generateAutoFinalVerdict(rowIdx);
        const currentVerdict = finalVerdictsState[rowIdx] || "";

        // Only update if there's a meaningful change and user hasn't manually edited
        if (newVerdict && newVerdict !== currentVerdict && !currentVerdict.includes("MANUAL:")) {
          updatedVerdicts[rowIdx] = newVerdict;
          hasChanges = true;
        } else if (currentVerdict) {
          updatedVerdicts[rowIdx] = currentVerdict;
        }
      });

      if (hasChanges) {
        setFinalVerdictsState(updatedVerdicts);
      }
    }
  }, [
    selectedCells,
    editableWalmartValues,
    walmartValidation,
    llmValidation,
    walmartLatestValidation, // ✅ NEW
    walmartComments,
    llmComments,
    walmartLatestComments, // ✅ NEW
    llmValues,
    tableRows,
    generateAutoFinalVerdict,
    currentId,
    finalVerdictsState
  ]);

  const computeFinalValues = (): EnhancedFinalValue[] => {
    return tableRows.map((row, rowIdx) => {
      const selectedCols = Object.keys(row).filter(
        (col) =>
          !["Attribute", "Validate LLM", "Validate Walmart", "Validate Walmart Latest"].includes(col) &&
          selectedCells.has(`${rowIdx}-${col}`)
      );
      if (selectedCells.has(`${rowIdx}-LLM`)) selectedCols.push("LLM");

      if (selectedCols.length === 0) {
        return {
          Attribute: row.Attribute,
          finalValue: "-",
          source: "-",
          walmartValidation: walmartValidation[rowIdx] || "",
          llmValidation: llmValidation[rowIdx] || "",
          walmartComment: walmartComments[rowIdx] || "",
          llmComment: llmComments[rowIdx] || "",
          finalVerdict: finalVerdictsState[rowIdx] || "", // ✅ NEW
          rawData: {},
        };
      }

      const values = selectedCols
        .map((col) => {
          if (col === "LLM") return cleanNaNValue(llmValues[rowIdx]);
          if (col === "Walmart")
            return cleanNaNValue(editableWalmartValues[rowIdx] || row[col]);
          if (col === "Walmart_Latest")
            return cleanNaNValue(row[col]);
          return cleanNaNValue(row[col]);
        })
        .filter((v) => v !== "-");

      const sources = selectedCols.map(col => {
        if (col === 'LLM') {
          return 'LLM'
        } else if (col === 'Walmart') {
          return 'Walmart'
        } else if (col === 'Walmart_Latest') {
          return 'Walmart Latest'
        } else {
          return 'LLM'
        }
      })  // Remove the .filter() since we never return "-" anymore


      return {
        Attribute: row.Attribute,
        finalValue: values.length > 0 ? values.join(", ") : "-",
        source: sources.length > 0 ? sources.join(" | ") : "-",
        walmartValidation: walmartValidation[rowIdx] || "",
        llmValidation: llmValidation[rowIdx] || "",
        walmartComment: walmartComments[rowIdx] || "",
        llmComment: llmComments[rowIdx] || "",
        finalValidation:
          [walmartValidation[rowIdx] || "", llmValidation[rowIdx] || ""]
            .filter(Boolean)
            .join(", ") || "-",
        finalComment:
          [walmartComments[rowIdx] || "", llmComments[rowIdx] || ""]
            .filter(Boolean)
            .map((c) =>
              c.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
            )
            .join(" | ") || "-",
        finalVerdict: finalVerdictsState[rowIdx] || "", // ✅ NEW
        rawData: {},
      };
    });
  };

  const finalValues = computeFinalValues();

  // ✅ ENHANCED CSV Export with ALL competitor values and Final Verdict
  const exportToWideFormatCSV = () => {
    try {
      const parsedResponse = JSON.parse(rawJson || "{}");
      const wideData: Record<string, string> = {};

      // User info
      try {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          const u = JSON.parse(storedUser);
          const submittedBy = u?.username || u?.name || u?.email || '';
          if (submittedBy) wideData['user'] = String(submittedBy);
        }
      } catch (e) {
        // ignore
      }

      // ✅ Basic info
      wideData['Timestamp'] = new Date().toISOString();
      wideData['Submission ID'] = preloadedRow?.id_str || '';
      wideData["Walmart URL"] = cleanNaNValue(parsedResponse.walmart?.url);
      wideData["Walmart_Latest URL"] = cleanNaNValue(parsedResponse.walmart_llm?.url);
      wideData["Product Type"] = cleanNaNValue(parsedResponse.product_type);
      wideData["Category"] = cleanNaNValue(parsedResponse.category);
      wideData["GTIN"] = cleanNaNValue(preloadedRow?.product_id);
      wideData["Product Name"] = cleanNaNValue(preloadedRow?.product_name);
      wideData["Item ID"] = cleanNaNValue(preloadedRow?.item_id);
      wideData["Final LLM Link"] = cleanNaNValue(llmSourceUrl);

      // ✅ Get all competitor columns dynamically
      const allCompetitors = new Set<string>();
      if (tableRows.length > 0) {
        Object.keys(tableRows[0]).forEach(col => {
          if (!["Attribute", "Walmart", "Walmart_Latest", "LLM", "Brand"].includes(col)) {
            allCompetitors.add(col);
          }
        });
      }

      // ✅ Add competitor source URLs to basic info
      allCompetitors.forEach(competitor => {
        wideData[`${competitor} Source URL`] = cleanNaNValue(competitorUrls[competitor] || "");
      });

      // Process all attributes
      const allAttributes = new Set<string>();
      tableRows.forEach(r => { if (r.Attribute) allAttributes.add(r.Attribute); });

      Array.from(allAttributes).forEach(attribute => {
        const rowIdx = tableRows.findIndex(r => r.Attribute === attribute);

        // ✅ Original data columns
        wideData[`${attribute} Initial prefilled values`] = cleanNaNValue(parsedResponse.walmart?.attributes?.[attribute]);
        wideData[`${attribute} Walmart_Latest values`] = cleanNaNValue(parsedResponse.walmart_llm?.attributes?.[attribute]);

        // ✅ LLM data
        const llmData = parsedResponse.llm_suggested?.[attribute];
        wideData[`${attribute} LLM Suggested value`] = processLlmValue(llmData?.value || llmData, attribute);
        wideData[`${attribute} LLM_suggested_url`] = cleanNaNValue(llmData?.source_url || "-");

        // ✅ Current values (edited or original)
        wideData[`${attribute}_Walmart_value`] = rowIdx !== -1 ? cleanNaNValue(editableWalmartValues[rowIdx] || tableRows[rowIdx]["Walmart"]) : "-";
        wideData[`${attribute}_Brand_value`] = cleanNaNValue(parsedResponse.brand?.attributes?.[attribute]);
        wideData[`${attribute}_WalmartLatest_value`] = rowIdx !== -1 ? cleanNaNValue(tableRows[rowIdx]["Walmart_Latest"]) : "-";
        wideData[`${attribute}_LLM_value`] = rowIdx !== -1 ? cleanNaNValue(llmValues[rowIdx]) : "-";

        // ✅ Add ALL competitor values
        allCompetitors.forEach(competitor => {
          wideData[`${attribute}_${competitor}_value`] = rowIdx !== -1 ? cleanNaNValue(tableRows[rowIdx][competitor] || "") : "-";
        });

        // ✅ Validation data
        wideData[`${attribute}_Walmart_Validation`] = rowIdx !== -1 ? cleanNaNValue(walmartValidation[rowIdx]) : "-";
        wideData[`${attribute}_WalmartLatest_Validation`] = rowIdx !== -1 ? cleanNaNValue(walmartLatestValidation[rowIdx]) : "-"; // ✅ NEW
        wideData[`${attribute}_LLM_Validation`] = rowIdx !== -1 ? cleanNaNValue(llmValidation[rowIdx]) : "-";
        wideData[`${attribute}_Final_Validation`] = rowIdx !== -1 ? ([walmartValidation[rowIdx] || "", llmValidation[rowIdx] || "",walmartLatestValidation[rowIdx] || ""].filter(Boolean).join(", ") || "-") : "-";

        // ✅ Comments data
        wideData[`${attribute}_Walmart_Comment`] = rowIdx !== -1 ? cleanNaNValue(walmartComments[rowIdx]) : "-";
        wideData[`${attribute}_LLM_Comment`] = rowIdx !== -1 ? cleanNaNValue(llmComments[rowIdx]) : "-";
        wideData[`${attribute}_Final_Comment`] = rowIdx !== -1 ? ([walmartComments[rowIdx] || "",walmartLatestComments[rowIdx] || "", llmComments[rowIdx] || ""].filter(Boolean).map(c => c.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())).join(' | ') || "-") : "-";

        // ✅ Final Verdict (the new column)
        wideData[`${attribute}_Final_Verdict`] = rowIdx !== -1 ? cleanNaNValue(finalVerdictsState[rowIdx] || "") : "-";
      });

      // ✅ Final combined values
      finalValues.forEach(fv => {
        if (fv.Attribute) {
          wideData[`${fv.Attribute}_Final_Choice`] = cleanNaNValue(fv.finalValue);
          wideData[`${fv.Attribute}_Final_Source`] = cleanNaNValue(fv.source);
          wideData[`${fv.Attribute}_Final_Verdict_Combined`] = cleanNaNValue(fv.finalVerdict || "");

          // ✅ Add selection status (including all competitors)
          const rowIdx = tableRows.findIndex(r => r.Attribute === fv.Attribute);
          if (rowIdx !== -1) {
            const selectedSources = [];
            if (selectedCells.has(`${rowIdx}-Walmart`)) selectedSources.push("Walmart");
            if (selectedCells.has(`${rowIdx}-Walmart_Latest`)) selectedSources.push("Walmart_Latest");
            if (selectedCells.has(`${rowIdx}-LLM`)) selectedSources.push("LLM");
            if (selectedCells.has(`${rowIdx}-Brand`)) selectedSources.push("Brand");

            // ✅ Add ALL competitor selections
            allCompetitors.forEach(competitor => {
              if (selectedCells.has(`${rowIdx}-${competitor}`)) {
                selectedSources.push(competitor);
              }
            });

            wideData[`${fv.Attribute}_Selected_Sources`] = selectedSources.join(", ") || "-";
          }
        }
      });

      // ✅ Summary statistics
      wideData['Total_Attributes'] = String(tableRows.length);
      wideData['Total_Selections'] = String(Array.from(selectedCells).length);
      wideData['Total_Competitors'] = String(allCompetitors.size);
      wideData['Competitor_Names'] = Array.from(allCompetitors).join(", ") || "-";
      wideData['Walmart_Yes_Count'] = String(Object.values(walmartValidation).filter(v => v === 'Yes').length);
      wideData['Walmart_No_Count'] = String(Object.values(walmartValidation).filter(v => v === 'No').length);
      wideData['WalmartLatest_Yes_Count'] = String(Object.values(walmartLatestValidation).filter(v => v === 'Yes').length); // ✅ NEW
      wideData['WalmartLatest_No_Count'] = String(Object.values(walmartLatestValidation).filter(v => v === 'No').length); // ✅ NEW
      wideData['LLM_Yes_Count'] = String(Object.values(llmValidation).filter(v => v === 'Yes').length);
      wideData['LLM_No_Count'] = String(Object.values(llmValidation).filter(v => v === 'No').length);
      wideData['Final_Verdicts_Count'] = String(Object.values(finalVerdictsState).filter(v => v && v.trim()).length);

      const headers = Object.keys(wideData);
      const values = Object.values(wideData);
      const csvContent = [
        headers.join(","),
        values.map(v => `"${String(v).replace(/"/g, '""').replace(/\n/g, ' ').replace(/\r/g, ' ')}"`).join(",")
      ].join("\n");

      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `complete-walmart-analysis-${preloadedRow?.id_str || 'unknown'}-${Date.now()}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (e) {
      console.error("export failed", e);
    }
  };

  // ✅ FIXED fetchResponse with proper initial loading
  const fetchResponse = async (): Promise<void> => {
    console.log('📡 fetchResponse called for:', preloadedRow?.id_str);
    setLoading(true);
    try {
      setTableRows([]);
      setEditableWalmartValues({});
      setLlmValues({});
      setCompetitorUrls({});
      setLlmSourceUrl("");
      setAttrMap({});
      setBrandUrl("");
      setRawJson("");

      if (!preloadedRow?.id_str) {
        console.warn('❌ fetchResponse called without preloadedRow.id_str', preloadedRow);
        return;
      }

      const form = new FormData();
      const prompt = `Please return cached product data for ${preloadedRow?.website_link || preloadedRow?.product_name || ''}`;
      const model = 'gemini-1.5-flash';
      form.append('prompt', prompt);
      form.append('model', model);
      form.append('website_link', String(preloadedRow.website_link || ''));
      form.append('id_str', String(preloadedRow.id_str));

      console.debug('🚀 POST /query/ form fields:', { prompt, model, website_link: preloadedRow.website_link, id_str: preloadedRow.id_str });
      const r = await fetch(`${API_BASE_URL}/query/`, { method: 'POST', body: form });
      const text = await r.text();
      let data: any = {};
      try { data = JSON.parse(text); } catch (err) { console.warn('⚠️ Non-JSON response from /query/:', text); }
      if (!r.ok) {
        const errMsg = (data && data.error) ? data.error : `HTTP ${r.status}`;
        throw new Error(`fetch failed: ${errMsg}`);
      }

      let rows: any[] = data.table || data.attributes || [];

      if ((!rows || rows.length === 0) && data.walmart) {
        const walmartAttrs = (data.walmart && data.walmart.attributes) || {};
        const brandAttrs = (data.brand && data.brand.attributes) || {};
        const competitors = data.competitors || [];
        const walmartLlmAttrs = (data.walmart_llm && data.walmart_llm.attributes) || {};

        const makeSafeName = (u: string) => {
          try {
            const hostname = new URL(u).hostname || "";
            return hostname.replace(/^www\./, "").split(".")[0] || u;
          } catch (e) {
            return u || "competitor";
          }
        };

        const extractedUrls: Record<string, string> = {};
        extractedUrls['Walmart'] = data.walmart.url || '';
        extractedUrls['Walmart_Latest'] = data.walmart_llm?.url || '';
        if (data.brand?.url) extractedUrls['Brand'] = data.brand.url;
        competitors.forEach((c: any) => {
          extractedUrls[makeSafeName(c.url)] = c.url || '';
        });

        const allAttrs = new Set<string>();
        Object.keys(walmartAttrs || {}).forEach(k => allAttrs.add(k));
        Object.keys(brandAttrs || {}).forEach(k => allAttrs.add(k));
        Object.keys(walmartLlmAttrs || {}).forEach(k => allAttrs.add(k));
        competitors.forEach((c: any) => {
          Object.keys((c && c.attributes) || {}).forEach(k => allAttrs.add(k));
        });

        const extras = ["Product Long Description", "Short Description", "Product Name", "Product Type"];
        extras.forEach(e => allAttrs.add(e));

        const builtRows: any[] = [];
        Array.from(allAttrs).forEach(attribute => {
          const row: Record<string, any> = { Attribute: attribute };
          row["Walmart"] = (walmartAttrs && (walmartAttrs[attribute] ?? (data.walmart.extra && data.walmart.extra[attribute]))) || "";
          row["Walmart_Latest"] = (walmartLlmAttrs && walmartLlmAttrs[attribute]) || "";
          row["Brand"] = (brandAttrs && (brandAttrs[attribute] ?? (data.brand && data.brand.extra && data.brand.extra[attribute]))) || "";
          competitors.forEach((c: any) => {
            const name = makeSafeName(c.url);
            row[name] = ((c && c.attributes && (c.attributes[attribute])) || (c && c.extra && c.extra[attribute])) || "";
          });
          builtRows.push(row);
        });

        rows = builtRows;
        setCompetitorUrls(extractedUrls);
      } else if (rows && rows.length > 0) {
        const sourceUrlRow = rows.find(r => r.Attribute === 'Source URL');
        const extractedUrls: Record<string, string> = {};

        if (sourceUrlRow) {
          Object.keys(sourceUrlRow).forEach(col => {
            if (col !== 'Attribute' && sourceUrlRow[col]) {
              extractedUrls[col] = sourceUrlRow[col];
            }
          });

          rows = rows.filter(r => r.Attribute !== 'Source URL');
        }

        setCompetitorUrls({ ...competitorUrls, ...extractedUrls });
      }

      setTableRows(rows || []);
      setRawJson(JSON.stringify(data || {}));

      const initEditable: Record<number, string> = {};
      const initOriginal: Record<number, string> = {};
      const initLlm: Record<number, string> = {};

      (rows || []).forEach((row: any, idx: number) => {
        initOriginal[idx] = cleanNaNValue(row.Walmart || '-');
        initEditable[idx] = cleanNaNValue(row.Walmart || '-');
        // ✅ Apply processLlmValue for LLM values
        const rawLlmValue = (data.llm_suggested && data.llm_suggested[row.Attribute]?.value) || row.LLM || '-';
        initLlm[idx] = processLlmValue(rawLlmValue, row.Attribute);
      });

      setOriginalWalmartValues(initOriginal);
      if (Object.keys(editableWalmartValues).length === 0) {
        setEditableWalmartValues(initEditable);
      }
      setLlmValues(initLlm);
      setBrandUrl(data.brand?.url || '');
      setLlmSourceUrl(data.llm_source_url || '');
      setAttrMap(data.attr_map || {});

    } catch (e) {
      console.error('❌ fetchResponse failed', e);
    } finally {
      setLoading(false);
    }
  };

  const generateInsights = async (): Promise<void> => {
    setInsightsLoading(true);
    try {
      const selectedData: any[] = [];
      tableRows.forEach((row, rowIdx) => {
        const selectedCols = Object.keys(row).filter((col) => ![
          "Attribute", "Validate LLM", "Validate Walmart", "Validate Walmart Latest", // ✅ NEW
          "Prefilled Value Comments", "Walmart Latest Comments", // ✅ NEW
          "LLM Comment", "Final Validation", "Final Comment", "Final Value", "Source", "Final Verdict"
        ].includes(col) && selectedCells.has(`${rowIdx}-${col}`));
        if (selectedCells.has(`${rowIdx}-LLM`)) selectedCols.push("LLM");
        if (selectedCols.length > 0) {
          const selectedValues = selectedCols.map(col => {
            if (col === 'LLM') return cleanNaNValue(llmValues[rowIdx]);
            if (col === 'Walmart') return cleanNaNValue(editableWalmartValues[rowIdx] || row[col]);
            if (col === 'Walmart_Latest') return cleanNaNValue(row[col]);
            return cleanNaNValue(row[col]);
          }).filter(v => v !== '-');
          selectedData.push({
            attribute: row.Attribute || `Unknown${rowIdx}`,
            selectedSources: selectedCols,
            selectedValues,
            walmartValidation: walmartValidation[rowIdx] || '',
            walmartLatestValidation: walmartLatestValidation[rowIdx] || '', // ✅ NEW
            llmValidation: llmValidation[rowIdx] || '',
            walmartComment: walmartComments[rowIdx] || '',
            walmartLatestComment: walmartLatestComments[rowIdx] || '', // ✅ NEW
            llmComment: llmComments[rowIdx] || '',
            finalVerdict: finalVerdictsState[rowIdx] || '' // ✅ NEW
          });
        }
      });

      const validationSummary = {
        walmart_yes: Object.values(walmartValidation).filter(v => v === 'yes').length,
        walmart_no: Object.values(walmartValidation).filter(v => v === 'no').length,
        walmart_latest_yes: Object.values(walmartLatestValidation).filter(v => v === 'yes').length, // ✅ NEW
        walmart_latest_no: Object.values(walmartLatestValidation).filter(v => v === 'no').length, // ✅ NEW
        llm_yes: Object.values(llmValidation).filter(v => v === 'yes').length,
        llm_no: Object.values(llmValidation).filter(v => v === 'no').length,
        total_selected: selectedData.length
      };

      const parsedResponse = JSON.parse(rawJson || '{}');
      const productInfo = {
        product_name: preloadedRow?.product_name || 'Unknown Product',
        category: parsedResponse.category || preloadedRow?.category || 'Unknown Category',
        walmart_url: parsedResponse.walmart?.url || preloadedRow?.website_link || '',
        product_type: parsedResponse.product_type || preloadedRow?.product_type || ''
      };

      const requestPayload = {
        product_name: productInfo.product_name,
        category: productInfo.category,
        product_attributes: {},
        site_summaries: selectedData,
        finalValues,
        attributeCount: tableRows.length,
        validationSummary,
        shortDescription: finalValues.find(i => i.Attribute === 'Short Description')?.finalValue || '',
        longDescription: finalValues.find(i => i.Attribute === 'Product Long Description')?.finalValue || ''
      };
      console.log('Insights request payload:', requestPayload);
      const resp = await fetch(`${API_BASE_URL}/claim/process_product`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestPayload)
      });

      if (!resp.ok) {
        const txt = await resp.text();
        throw new Error(`Insights API failed: ${txt}`);
      }

      const apiResult = await resp.json();
      setShortSummary(apiResult.short_summary || 'Analysis completed successfully.');
      // ✅ UPDATED: Convert markdown to HTML
      const rawLongSummary = apiResult.long_summary || 'Detailed insights have been generated based on your selected data.';
      setDetailedSummaryRaw(rawLongSummary);
      const htmlSummary = await marked(rawLongSummary);
      setDetailedSummary(htmlSummary);

      setInsightsGenerated(true);

    } catch (e) {
      console.warn('insights failed, using fallback', e);
      setShortSummary(`Analysis of ${tableRows.length} attributes completed. ${Array.from(selectedCells).length} selections made.`);
      setDetailedSummary(`Competitive analysis has been performed on the selected product attributes.`);
      setInsightsGenerated(true);

    } finally {
      setInsightsLoading(false);
    }

  };

  const handleCellToggle = (rowIdx: number, col: string) => {
    const key = `${rowIdx}-${col}`;
    const updated = new Set(selectedCells);
    if (updated.has(key)) updated.delete(key); else updated.add(key);
    setSelectedCells(updated);
  };

  const [shortSummaryEdited, setShortSummaryEdited] = useState(false);
  const [detailedSummaryEdited, setDetailedSummaryEdited] = useState(false);


  const handleRowToggle = (rowIdx: number, row: Record<string, string>) => {
    const updated = new Set(selectedCells);
    const rowKeys = Object.keys(row).filter(col => ![
      'Attribute', 'Validate LLM', 'Validate Walmart', 'Validate Walmart Latest', // ✅ NEW
      'Prefilled Value Comments', 'Walmart Latest Comments', // ✅ NEW
      'LLM Comment', 'Final Verdict'
    ].includes(col)).map(col => `${rowIdx}-${col}`).concat([`${rowIdx}-LLM`]);
    const allSelected = rowKeys.every(k => updated.has(k));
    if (allSelected) rowKeys.forEach(k => updated.delete(k)); else rowKeys.forEach(k => updated.add(k));
    setSelectedCells(updated);
  };

  const clearSelection = () => {
    setSelectedCells(new Set());
    setEditableWalmartValues({});
    setWalmartValidation({});
    setLlmValidation({});
    setWalmartComments({});
    setLlmComments({});
    setWalmartLatestValidation({}); // ✅ NEW
    setWalmartLatestComments({}); // ✅ NEW
    setFinalVerdictsState({});

    if (currentId) {
      clearSelections(currentId);
    }
  };



  return (
    <div className="h-screen flex flex-col">
      <div className="rounded-2xl bg-white dark:bg-gray-900 shadow-lg border border-gray-200 dark:border-gray-700 transition-colors overflow-hidden flex-1 flex flex-col">
        <ResponseControls
          preloadedRow={preloadedRow}
          loading={loading}
          fetchResponse={fetchResponse}
          exportToWideFormatCSV={exportToWideFormatCSV}
          generateInsights={generateInsights}
          clearSelection={clearSelection}
        />

        {loading && (
          <div className="flex items-center gap-4 text-gray-600 dark:text-gray-400 p-8 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
            <Loader2 className="animate-spin w-6 h-6 text-blue-500" />
            <span className="italic font-medium">Fetching and analyzing product data...</span>
          </div>
        )}

        <div className="p-6 flex-1 overflow-auto">
          <ResponseTable
            tableRows={tableRows}
            selectedCells={selectedCells}
            handleCellToggle={handleCellToggle}
            handleRowToggle={handleRowToggle}
            editableWalmartValues={editableWalmartValues}
            originalWalmartValues={originalWalmartValues}
            editingCell={editingCell}
            setEditingCell={setEditingCell}
            saveEdit={(rowIdx: number) => {
              setEditingCell(null);
              setTableRows(prev => prev.map((r, i) => i === rowIdx ? { ...r, Walmart: cleanNaNValue(editableWalmartValues[rowIdx]) } : r));
            }}
            cancelEdit={() => setEditingCell(null)}
            setEditableWalmartValues={setEditableWalmartValues}
            llmValues={llmValues}
            rawJson={rawJson}
            competitorUrls={competitorUrls}
            brandUrl={brandUrl}
            llmSourceUrl={llmSourceUrl}
            walmartValidation={walmartValidation}
            llmValidation={llmValidation}
            setWalmartValidation={setWalmartValidation}
            setLlmValidation={setLlmValidation}
            walmartComments={walmartComments}
            llmComments={llmComments}
            setWalmartComments={setWalmartComments}
            setLlmComments={setLlmComments}
            // ✅ NEW: Pass Walmart Latest validation and comments props
            walmartLatestValidation={walmartLatestValidation}
            walmartLatestComments={walmartLatestComments}
            setWalmartLatestValidation={setWalmartLatestValidation}
            setWalmartLatestComments={setWalmartLatestComments}
            finalValues={finalValues}
            loading={loading}
            attrMap={attrMap}
            exportToWideFormatCSV={exportToWideFormatCSV}
            generateInsights={generateInsights}
            insightsLoading={insightsLoading}
            finalVerdictsState={finalVerdictsState}
            setFinalVerdictsState={setFinalVerdictsState}
            editingFinalVerdict={editingFinalVerdict}
            setEditingFinalVerdict={setEditingFinalVerdict}
            generateAutoFinalVerdict={generateAutoFinalVerdict}
          />

          {!insightsLoading && insightsGenerated && (shortSummary || detailedSummary) && (
            <div className="space-y-8 transition-all duration-500 mb-10 mt-6">
              {shortSummary && (
                <div className="p-8 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/50 dark:to-indigo-900/50 rounded-2xl border border-blue-200 dark:border-blue-700 shadow-2xl relative">
                  <h3 className="font-extrabold text-2xl md:text-3xl text-blue-800 dark:text-blue-300 mb-3 flex items-center gap-2">
                    Short Summary
                    {shortSummaryEdited && <span className="text-sm text-yellow-600 dark:text-yellow-400 font-semibold">(edited)</span>}
                  </h3>
                  <textarea
                    className="w-full bg-transparent text-gray-800 dark:text-gray-200 leading-relaxed text-base md:text-lg border border-gray-300 dark:border-gray-600 rounded-md p-2 resize-none"
                    value={shortSummary}
                    onChange={(e) => {
                      setShortSummary(e.target.value);
                      setShortSummaryEdited(true);
                    }}
                    rows={4}
                  />
                </div>
              )}
              {/* ...existing code... */}



              {/* {detailedSummary && (

                <div className="p-8 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/50 dark:to-emerald-900/50 rounded-2xl border border-green-200 dark:border-green-700 shadow-2xl relative">

                  <h3 className="font-extrabold text-2xl md:text-3xl text-green-800 dark:text-green-300 mb-3 flex items-center gap-2">

                    Detailed Summary

                    {detailedSummaryEdited && (

                      <span className="text-sm text-yellow-600 dark:text-yellow-400 font-semibold">(edited)</span>

                    )}

                  </h3>



                  <textarea

                    className="w-full bg-transparent text-gray-800 dark:text-gray-200 leading-relaxed text-sm md:text-base border border-gray-300 dark:border-gray-600 rounded-md p-2 resize-none"

                    value={detailedSummary}

                    onChange={(e) => {

                      setDetailedSummary(e.target.value);

                      setDetailedSummaryEdited(true);

                    }}

                    rows={Math.max(4, detailedSummary.split("\n").length)} // auto adjust rows based on content

                  />



                </div>

              )}



              {/* ...new code... */}



              {detailedSummary && (

                <div className="p-8 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/50 dark:to-emerald-900/50 rounded-2xl border border-green-200 dark:border-green-700 shadow-2xl relative">

                  <div className="flex justify-between items-center mb-3">

                    <h3 className="font-extrabold text-2xl md:text-3xl text-green-800 dark:text-green-300 flex items-center gap-2">

                      Detailed Summary

                      {detailedSummaryEdited && (

                        <span className="text-sm text-yellow-600 dark:text-yellow-400 font-semibold">(edited)</span>

                      )}

                    </h3>

                    <button

                      onClick={() => setDetailedSummaryEditMode(!detailedSummaryEditMode)}

                      className="px-3 py-1 text-xs bg-green-200 dark:bg-green-700 text-green-800 dark:text-green-200 rounded-md hover:bg-green-300 dark:hover:bg-green-600 transition-colors"

                    >

                      {detailedSummaryEditMode ? "View" : "Edit"}

                    </button>

                  </div>
                  {detailedSummaryEditMode ? (
                    <textarea
                      className="w-full bg-transparent text-gray-800 dark:text-gray-200 leading-relaxed text-sm md:text-base border border-gray-300 dark:border-gray-600 rounded-md p-2 resize-none"
                      value={detailedSummaryRaw}
                      onChange={(e) => {
                        const value = e.target.value;
                        setDetailedSummaryRaw(value);
                        setDetailedSummaryEdited(true);

                        // Handle both sync and async return types
                        const result = marked.parseInline(value);
                        if (result instanceof Promise) {
                          // It's a Promise - handle async
                          result.then((htmlContent: string) => {
                            setDetailedSummary(htmlContent);
                          }).catch(() => {
                            setDetailedSummary(value); // Fallback to raw text
                          });
                        } else {
                          // It's a string - handle sync
                          setDetailedSummary(result);
                        }
                      }}
                      rows={Math.max(4, detailedSummaryRaw.split("\n").length)}
                    />
                  ) : (
                    <div
                      className="w-full bg-transparent text-gray-800 dark:text-gray-200 leading-relaxed text-sm md:text-base prose prose-sm max-w-none dark:prose-invert"
                      dangerouslySetInnerHTML={{ __html: detailedSummary }}
                    />
                  )}
                </div>
              )}
            </div>
          )}
          {tableRows.length > 0 && !loading && (
            <FeedbackForm
              responseText={rawJson}
              id_str={preloadedRow?.id_str || ''}
              finalValues={finalValues}
              shortSummary={shortSummary}
              detailedSummary={detailedSummary}
            />
          )}
        </div>
      </div>
    </div >

  );

}


